import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import path from "node:path";
import { defineConfig } from "vite";
import vueDevTools from "vite-plugin-vue-devtools";

// https://vite.dev/config/
export default defineConfig({
  build: {
    outDir: "xw-vet-open-ui",
  },
  plugins: [vue(), vueJsx(), vueDevTools()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"), // 确保路径正确
    },
  },
  server: {
    host: "0.0.0.0", // 监听所有网络接口
    port: 3000, // 确保端口一致
    strictPort: true, // 禁止自动切换端口
    proxy: {
      // 代理所有 /api 请求到后端服务器
      "/api": {
        target: "https://open.haoshouyi.com/api/v1",
        // target: 'http://*************:8000/api/v1',
        // target: 'http://localhost:8000/api/v1',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
});
