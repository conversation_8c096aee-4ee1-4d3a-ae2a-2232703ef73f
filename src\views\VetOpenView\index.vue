<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue'

// Intersection Observer for scroll animations
const observerRef = ref<IntersectionObserver | null>(null)

onMounted(() => {
  // Create intersection observer for scroll-based animations
  observerRef.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in')
        }
      })
    },
    {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }
  )

  // Observe all animatable elements
  const animatableElements = document.querySelectorAll('.tech-animate')
  animatableElements.forEach((el) => {
    observerRef.value?.observe(el)
  })

  // Add floating particles effect
  createFloatingParticles()

  // Add tech grid lines
  createTechGrid()
})

onUnmounted(() => {
  observerRef.value?.disconnect()
})

// Create floating particles for tech aesthetic
const createFloatingParticles = () => {
  const particleContainer = document.querySelector('.vet-open-view')
  if (!particleContainer) return

  for (let i = 0; i < 15; i++) {
    const particle = document.createElement('div')
    particle.className = 'tech-particle'
    particle.style.left = Math.random() * 100 + '%'
    particle.style.animationDelay = Math.random() * 8 + 's'
    particle.style.animationDuration = (Math.random() * 8 + 12) + 's'
    particleContainer.appendChild(particle)
  }
}

// Create tech grid background
const createTechGrid = () => {
  const container = document.querySelector('.vet-open-view')
  if (!container) return

  const gridOverlay = document.createElement('div')
  gridOverlay.className = 'tech-grid-overlay'
  container.appendChild(gridOverlay)
}
</script>

<template>
  <div class="vet-open-view">
    <!-- Tech background elements will be added by JS -->

    <h1 class="tech-animate tech-title-glow">AI开放平台</h1>
    <p class="tech-animate tech-text-enhance">
      打开
      AI开放平台，全面赋能宠物健康产业生态，为你的业务注入AI活力。无论是宠物智能用品、保险机构、医疗设备商，还是宠物医院、营养品牌或零售平台，我们都提供安全合规、即插即用的智能化解决方案，助力你的业务实现创新与增长！
    </p>
    <button class="trial-button tech-animate tech-button-enhance">
      <span class="button-text">立即体验</span>
      <div class="button-tech-glow"></div>
      <div class="button-ripple"></div>
    </button>
  </div>
</template>
<style scoped src="./index.css"></style>
