.home-view {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

/* Scroll-based animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Tech grid background */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 87, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 87, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
  z-index: 1;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* Floating particles */
.floating-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #0057ff, transparent);
  border-radius: 50%;
  animation: float-up linear infinite;
  z-index: 2;
}

@keyframes float-up {
  0% {
    opacity: 0;
    transform: translateY(100vh) scale(0);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(-100px) scale(1);
  }
}

.home-page-1 {
  background: url("/home/<USER>") no-repeat center center;
  background-size: cover;
  display: flex;
  height: 100dvh;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  letter-spacing: 0.2dvw;
  position: relative;
  overflow: hidden;

  > h1 {
    font-weight: bold;
    font-size: var(--h1-font-large-size);
    z-index: 3;
    position: relative;
  }

  .tech-subtitle {
    font-weight: bold;
    font-size: 1.5dvw;
    color: var(--el-text-color-regular);
    margin-bottom: 4dvh;
    z-index: 3;
    position: relative;
    text-shadow: 0 0 20px rgba(0, 87, 255, 0.3);
  }

  .brand-info {
    font-weight: bold;
    font-size: 1dvw;
    display: flex;
    align-items: center;
    color: var(--el-text-color-regular);
    z-index: 3;
    position: relative;

    .logo-glow {
      width: 2.5dvw;
      filter: drop-shadow(0 0 10px rgba(0, 87, 255, 0.5));
      transition: all 0.3s ease;
    }

    .logo-glow:hover {
      filter: drop-shadow(0 0 20px rgba(0, 87, 255, 0.8));
      transform: scale(1.1);
    }

    .brand-text {
      margin-left: 0.5dvw;
      text-shadow: 0 0 10px rgba(0, 87, 255, 0.3);
    }
  }
}

/* Scroll indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2dvh;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 24px;
  height: 24px;
  border: 2px solid #0057ff;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
  animation: glow-pulse 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    border-color: #0057ff;
    box-shadow: 0 0 5px rgba(0, 87, 255, 0.3);
  }
  50% {
    border-color: #66b3ff;
    box-shadow: 0 0 20px rgba(0, 87, 255, 0.6);
  }
}

.home-page-1 .tech-subtitle {
  animation: fade-in-down 1.2s cubic-bezier(0.4,2,0.6,1) 0.8s both;
}
.home-page-1 .brand-info {
  animation: fade-in-down 1.2s cubic-bezier(0.4,2,0.6,1) 1.2s both;
}

@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-2vw);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.home-page-2 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100dvh;
  padding: 10dvh 22dvw;
  background: linear-gradient(135deg,
    rgba(0, 87, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(0, 87, 255, 0.02) 100%);
  position: relative;

  .content-container {
    max-width: 100%;
    text-align: center;
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4dvh;
    gap: 2dvw;

    h3 {
      font-size: 2.5dvw;
      font-weight: bold;
      color: #0057ff;
      margin: 0;
      text-shadow: 0 0 10px rgba(0, 87, 255, 0.3);
    }

    .tech-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg,
        transparent 0%,
        #0057ff 50%,
        transparent 100%);
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 10px;
        height: 4px;
        background: #0057ff;
        border-radius: 2px;
        box-shadow: 0 0 10px rgba(0, 87, 255, 0.5);
      }
    }
  }

  > p {
    line-height: 3dvw;
    letter-spacing: 0.1dvw;
    font-size: 1.6dvw;
    font-weight: bold;
    color: var(--el-text-color-regular);
    text-indent: 1.6dvw;
    margin-bottom: 2dvh;
    transition: all 0.3s ease;

    &:hover {
      color: #0057ff;
      text-shadow: 0 0 5px rgba(0, 87, 255, 0.3);
    }
  }
}

.home-page-3 {
  background: linear-gradient(
    135deg,
    rgba(0, 87, 255, 0.1) 0%,
    rgba(96, 114, 203, 0.15) 30%,
    rgba(64, 150, 162, 0.1) 70%,
    rgba(0, 87, 255, 0.05) 100%
  );
  padding: 5dvh 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230057ff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    animation: pattern-move 30s linear infinite;
  }
}

@keyframes pattern-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* Enhanced testimonial cards */
.testimonial-card {
  min-height: 100dvh;
  display: flex;
  gap: 3dvw;
  justify-content: center;
  align-items: center;
  width: 80dvw;
  margin: auto;
  position: relative;
  padding: 5dvh 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-10px);

    .card-glow {
      opacity: 1;
      transform: scale(1.05);
    }

    .testimonial-image {
      transform: scale(1.05);
      box-shadow: 0 20px 40px rgba(0, 87, 255, 0.3);
    }
  }

  .card-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    width: 100%;
    height: 80%;
    background: radial-gradient(
      ellipse at center,
      rgba(0, 87, 255, 0.1) 0%,
      rgba(0, 87, 255, 0.05) 50%,
      transparent 70%
    );
    border-radius: 20px;
    opacity: 0;
    transition: all 0.6s ease;
    z-index: 1;
  }

  .testimonial-image {
    width: 30dvw;
    border-radius: var(--el-border-radius-round);
    border: 3px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 2;
    position: relative;
  }

  .testimonial-content {
    flex: 1;
    color: white;
    letter-spacing: 0.1dvw;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 2;
    position: relative;

    .testimonial-header {
      margin-bottom: 3dvh;

      .testimonial-name {
        font-size: 2.5dvw;
        margin: 0 0 1dvh 0;
        color: #0057ff;
        text-shadow: 0 0 10px rgba(0, 87, 255, 0.5);
        font-weight: bold;
      }

      .testimonial-titles p {
        margin: 0.2rem 0;
        font-size: 1.2dvw;
        line-height: 1.7dvw;
        color: rgba(255, 255, 255, 0.9);
        transition: color 0.3s ease;

        &:hover {
          color: #66b3ff;
        }
      }
    }

    .testimonial-quote {
      position: relative;

      .quote-mark {
        font-size: 3.2dvw;
        color: #0057ff;
        text-shadow: 0 0 15px rgba(0, 87, 255, 0.7);
        font-weight: bold;

        &.human-aside-start {
          transform: translateY(3dvh);
        }

        &.human-aside-end {
          text-align: right;
        }
      }

      .quote-content p {
        margin: 0.5rem 0;
        font-size: 1.2dvw;
        line-height: 1.8dvw;
        color: rgba(255, 255, 255, 0.95);
        transition: all 0.3s ease;

        &:hover {
          color: white;
          text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

/* Reverse layout for alternating testimonials */
.testimonial-reverse {
  flex-direction: row-reverse;

  .testimonial-content {
    text-align: right;

    .testimonial-quote .quote-mark.human-aside-end {
      text-align: left;
    }
  }
}

.trial-button {
  padding: 0.8dvw 2.5dvw;
  font-size: 1.2dvw;
  border-radius: 2vw;
  color: #fff;
  cursor: pointer;
  background: linear-gradient(
    135deg,
    rgba(0, 87, 255, 0.8) 0%,
    rgba(0, 150, 255, 0.9) 50%,
    rgba(0, 87, 255, 0.8) 100%
  );
  box-shadow:
    0 0 20px rgba(0, 87, 255, 0.4),
    0 0 40px rgba(0, 87, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  font-weight: 900;
  letter-spacing: 0.1em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(16px) saturate(160%);
  -webkit-backdrop-filter: blur(16px) saturate(160%);
  border: 2px solid rgba(0, 87, 255, 0.5);
  margin-block: 4dvh 10dvh;

  .button-text {
    position: relative;
    z-index: 2;
  }

  .button-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 87, 255, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.3) 0%,
      transparent 70%
    );
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
    z-index: 1;
  }

  &:hover {
    box-shadow:
      0 0 30px rgba(0, 87, 255, 0.6),
      0 0 60px rgba(0, 87, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-3px) scale(1.05);

    .button-glow {
      opacity: 1;
    }

    &::before {
      width: 300px;
      height: 300px;
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
      0 0 20px rgba(0, 87, 255, 0.8),
      0 0 40px rgba(0, 87, 255, 0.4);
  }
}

.animated-title {
  display: inline-block;
  white-space: pre;
}
.animated-title .char {
  display: inline-block;
  opacity: 0;
  transform: translate(var(--x, 0), var(--y, 0)) scale(1.8) rotate(var(--r, 0deg));
  animation: fly-in 1.2s cubic-bezier(0.4,2,0.6,1) forwards;
}
.animated-title .char:nth-child(1) { --x: -3vw; --y: -4vw; --r: -60deg; animation-delay: 0.05s; }
.animated-title .char:nth-child(2) { --x: 2vw; --y: -5vw; --r: 40deg; animation-delay: 0.15s; }
.animated-title .char:nth-child(3) { --x: -4vw; --y: 3vw; --r: 30deg; animation-delay: 0.25s; }
.animated-title .char:nth-child(4) { --x: 4vw; --y: -2vw; --r: -30deg; animation-delay: 0.35s; }
.animated-title .char:nth-child(5) { --x: -2vw; --y: 5vw; --r: 50deg; animation-delay: 0.45s; }
.animated-title .char:nth-child(6) { --x: 3vw; --y: 4vw; --r: -50deg; animation-delay: 0.55s; }
.animated-title .char:nth-child(7) { --x: -5vw; --y: -2vw; --r: 60deg; animation-delay: 0.65s; }
.animated-title .char:nth-child(8) { --x: 5vw; --y: 3vw; --r: -40deg; animation-delay: 0.75s; }
.animated-title .char:nth-child(9) { --x: 0vw; --y: -6vw; --r: 20deg; animation-delay: 0.85s; }
.animated-title .char:nth-child(10) { --x: 0vw; --y: 6vw; --r: -20deg; animation-delay: 0.95s; }

@keyframes fly-in {
  0% {
    opacity: 0;
    filter: blur(12px) brightness(1.8) drop-shadow(0 0 24px #0057ff);
    transform: translate(var(--x, 0), var(--y, 0)) scale(1.8) rotate(var(--r, 0deg));
  }
  60% {
    opacity: 1;
    filter: blur(2px) brightness(1.2) drop-shadow(0 0 12px #66b3ff);
    transform: translate(0, 0) scale(1.1) rotate(0deg);
  }
  100% {
    opacity: 1;
    filter: blur(0) brightness(1) drop-shadow(0 0 0 #fff0);
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .home-page-1 {
    padding: 0 5vw;

    > h1 {
      font-size: 12vw;
    }

    .tech-subtitle {
      font-size: 4vw;
      text-align: center;
    }

    .brand-info {
      font-size: 3vw;

      .logo-glow {
        width: 8vw;
      }
    }
  }

  .home-page-2 {
    padding: 10vh 5vw;

    .section-header {
      flex-direction: column;
      gap: 2vh;

      h3 {
        font-size: 6vw;
      }

      .tech-line {
        width: 50vw;
      }
    }

    > p {
      font-size: 4vw;
      line-height: 6vw;
      text-indent: 4vw;
    }
  }

  .testimonial-card {
    flex-direction: column;
    width: 90vw;
    gap: 5vh;
    padding: 8vh 0;

    &.testimonial-reverse {
      flex-direction: column;
    }

    .testimonial-image {
      width: 60vw;
      max-width: 300px;
    }

    .testimonial-content {
      text-align: center;

      .testimonial-header .testimonial-name {
        font-size: 6vw;
      }

      .testimonial-header .testimonial-titles p {
        font-size: 3.5vw;
        line-height: 5vw;
      }

      .testimonial-quote {
        .quote-mark {
          font-size: 8vw;
        }

        .quote-content p {
          font-size: 3.5vw;
          line-height: 5.5vw;
        }
      }
    }
  }

  .trial-button {
    padding: 3vw 6vw;
    font-size: 4vw;
    border-radius: 6vw;
  }

  .scroll-indicator {
    bottom: 5vh;
  }

  .floating-particle {
    width: 2px;
    height: 2px;
  }
}

/* Tablet Responsiveness */
@media (max-width: 1024px) and (min-width: 769px) {
  .home-page-1 {
    > h1 {
      font-size: 10vw;
    }

    .tech-subtitle {
      font-size: 3vw;
    }

    .brand-info {
      font-size: 2.5vw;

      .logo-glow {
        width: 5vw;
      }
    }
  }

  .home-page-2 {
    padding: 8vh 15vw;

    .section-header h3 {
      font-size: 4vw;
    }

    > p {
      font-size: 3vw;
      line-height: 4.5vw;
      text-indent: 3vw;
    }
  }

  .testimonial-card {
    width: 85vw;

    .testimonial-image {
      width: 35vw;
    }

    .testimonial-content {
      .testimonial-header .testimonial-name {
        font-size: 4vw;
      }

      .testimonial-header .testimonial-titles p {
        font-size: 2.5vw;
        line-height: 3.5vw;
      }

      .testimonial-quote {
        .quote-mark {
          font-size: 5vw;
        }

        .quote-content p {
          font-size: 2.5vw;
          line-height: 3.8vw;
        }
      }
    }
  }

  .trial-button {
    padding: 2vw 4vw;
    font-size: 2.5vw;
  }
}

/* Performance optimizations for animations */
@media (prefers-reduced-motion: reduce) {
  .scroll-animate,
  .floating-particle,
  .tech-grid,
  .animated-title .char {
    animation: none !important;
    transition: none !important;
  }

  .scroll-animate {
    opacity: 1;
    transform: none;
  }
}
